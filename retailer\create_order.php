<?php
// Start session
session_start();

// Check if user is logged in and has retailer role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'retailer') {
    header("Location: ../login.php");
    exit();
}

// Connect to database
require_once '../config/db_connect.php';

// Initialize variables
$suppliers = [];
$products = [];
$success_message = '';
$error_message = '';

// Get all suppliers
try {
    if (!isset($db_connection_error)) {
        $stmt = $conn->prepare("SELECT id, fullname, company_name FROM users WHERE role = 'supplier' AND status = 'active'");
        $stmt->execute();
        $suppliers = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    $error_message = "Error fetching suppliers: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_order'])) {
    try {
        // Validate input
        if (empty($_POST['supplier_id']) || empty($_POST['shipping_address']) || empty($_POST['products'])) {
            throw new Exception("Please fill all required fields");
        }

        $supplier_id = intval($_POST['supplier_id']);
        $shipping_address = trim($_POST['shipping_address']);
        $notes = trim($_POST['notes'] ?? '');
        $products_data = $_POST['products'];
        $quantities = $_POST['quantities'];

        // Start transaction
        $conn->beginTransaction();

        // Generate order number
        $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        
        // Calculate total amount
        $total_amount = 0;
        $order_items = [];
        
        foreach ($products_data as $index => $product_id) {
            if (empty($product_id) || empty($quantities[$index])) continue;
            
            $product_id = intval($product_id);
            $quantity = intval($quantities[$index]);
            
            // Get product price
            $stmt = $conn->prepare("SELECT price FROM products WHERE id = ? AND status = 'active'");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if (!$product) {
                throw new Exception("Invalid product selected");
            }
            
            $unit_price = $product['price'];
            $item_total = $unit_price * $quantity;
            $total_amount += $item_total;
            
            $order_items[] = [
                'product_id' => $product_id,
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'total_price' => $item_total
            ];
        }
        
        if (empty($order_items)) {
            throw new Exception("Please add at least one product to the order");
        }

        // Insert order
        $stmt = $conn->prepare("INSERT INTO orders (retailer_id, supplier_id, order_number, status, total_amount, shipping_address, notes, created_at, updated_at) 
                                VALUES (?, ?, ?, 'pending', ?, ?, ?, NOW(), NOW())");
        $stmt->execute([
            $_SESSION['user_id'],
            $supplier_id,
            $orderNumber,
            $total_amount,
            $shipping_address,
            $notes
        ]);
        
        $order_id = $conn->lastInsertId();
        
        // Insert order items
        foreach ($order_items as $item) {
            $stmt = $conn->prepare("INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) 
                                    VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $order_id,
                $item['product_id'],
                $item['quantity'],
                $item['unit_price'],
                $item['total_price']
            ]);
        }
        
        // Commit transaction
        $conn->commit();
        
        $success_message = "Order #$orderNumber created successfully!";
        
        // Redirect to order details
        $_SESSION['success'] = $success_message;
        header("Location: orders.php");
        exit();
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $error_message = $e->getMessage();
    }
}

// Get products by supplier if supplier is selected
if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
    $supplier_id = intval($_GET['supplier_id']);
    try {
        $stmt = $conn->prepare("SELECT id, name, sku, price, stock_quantity, image_url 
                               FROM products 
                               WHERE supplier_id = ? AND status = 'active' AND stock_quantity > 0
                               ORDER BY name");
        $stmt->execute([$supplier_id]);
        $products = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error_message = "Error fetching products: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiGit - Create Order</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header/Navigation -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="/" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                        </div>
                        <nav class="ml-6 flex space-x-8">
                            <a href="dashboard.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Dashboard
                            </a>
                            <a href="inventory.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Inventory
                            </a>
                            <a href="orders.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-primary text-sm font-medium text-gray-900">
                                Orders
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Page Header -->
                    <div class="mb-6 flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-semibold text-gray-900">Create New Order</h1>
                            <p class="mt-1 text-sm text-gray-600">Place an order with your suppliers</p>
                        </div>
                        <a href="orders.php" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-button shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                            <i class="ri-arrow-left-line mr-2"></i> Back to Orders
                        </a>
                    </div>

                    <!-- Error/Success Messages -->
                    <?php if (!empty($error_message)): ?>
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-error-warning-line text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700"><?php echo htmlspecialchars($error_message); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success_message)): ?>
                        <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-checkbox-circle-line text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-green-700"><?php echo htmlspecialchars($success_message); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Order Form -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Order Information</h3>
                            <p class="mt-1 text-sm text-gray-500">Fill in the details to place your order</p>
                        </div>
                        
                        <form method="POST" action="create_order.php" id="orderForm">
                            <div class="px-4 py-5 sm:p-6">
                                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <!-- Supplier Selection -->
                                    <div class="col-span-2 sm:col-span-1">
                                        <label for="supplier_id" class="block text-sm font-medium text-gray-700">Select Supplier</label>
                                        <select id="supplier_id" name="supplier_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md" onchange="loadSupplierProducts(this.value)">
                                            <option value="">-- Select a Supplier --</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <option value="<?php echo $supplier['id']; ?>" <?php echo (isset($_GET['supplier_id']) && $_GET['supplier_id'] == $supplier['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($supplier['company_name'] ? $supplier['company_name'] : $supplier['fullname']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Shipping Address -->
                                    <div class="col-span-2">
                                        <label for="shipping_address" class="block text-sm font-medium text-gray-700">Shipping Address</label>
                                        <textarea id="shipping_address" name="shipping_address" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"><?php echo htmlspecialchars($_SESSION['shipping_address'] ?? ''); ?></textarea>
                                    </div>

                                    <!-- Order Notes -->
                                    <div class="col-span-2">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Order Notes</label>
                                        <textarea id="notes" name="notes" rows="2" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" placeholder="Special instructions or notes for this order"></textarea>
                                    </div>
                                </div>

                                <!-- Product Selection -->
                                <div class="mt-6">
                                    <h4 class="text-md font-medium text-gray-900 mb-3">Order Items</h4>
                                    
                                    <?php if (empty($products) && isset($_GET['supplier_id'])): ?>
                                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <i class="ri-information-line text-yellow-400"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm text-yellow-700">No products available from this supplier or all products are out of stock.</p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php elseif (!isset($_GET['supplier_id'])): ?>
                                        <div class="text-center py-6 bg-gray-50 rounded-lg">
                                            <i class="ri-shopping-cart-line text-4xl text-gray-300 mb-2"></i>
                                            <p class="text-gray-500">Please select a supplier to view available products</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-50">
                                                    <tr>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    <?php foreach ($products as $index => $product): ?>
                                                        <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <div class="flex items-center">
                                                                    <input type="checkbox" name="products[]" value="<?php echo $product['id']; ?>" id="product_<?php echo $product['id']; ?>" class="product-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" onchange="updateQuantityField(this, <?php echo $product['id']; ?>)">
                                                                    <div class="ml-3 flex items-center">
                                                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                                                                            <?php if (!empty($product['image_url'])): ?>
                                                                                <img src="../uploads/products/thumbnails/<?php echo htmlspecialchars($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="h-10 w-10 rounded-md object-cover">
                                                                            <?php else: ?>
                                                                                <i class="ri-shopping-bag-line text-gray-400 text-lg"></i>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                        <div class="ml-4">
                                                                            <label for="product_<?php echo $product['id']; ?>" class="text-sm font-medium text-gray-900 cursor-pointer"><?php echo htmlspecialchars($product['name']); ?></label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($product['sku']); ?></div>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <div class="text-sm text-gray-900">$<?php echo number_format($product['price'], 2); ?></div>
                                                                <input type="hidden" id="price_<?php echo $product['id']; ?>" value="<?php echo $product['price']; ?>">
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <div class="text-sm text-gray-900"><?php echo $product['stock_quantity']; ?> units</div>
                                                                <input type="hidden" id="max_qty_<?php echo $product['id']; ?>" value="<?php echo $product['stock_quantity']; ?>">
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <input type="number" name="quantities[]" id="qty_<?php echo $product['id']; ?>" min="1" max="<?php echo $product['stock_quantity']; ?>" value="1" class="quantity-input block w-20 border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" disabled onchange="updateItemTotal(<?php echo $product['id']; ?>)">
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <div class="text-sm font-medium text-gray-900" id="total_<?php echo $product['id']; ?>">$0.00</div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                                <tfoot class="bg-gray-50">
                                                    <tr>