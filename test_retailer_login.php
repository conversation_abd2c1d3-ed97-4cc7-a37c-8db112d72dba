<?php
// Test script to verify retailer login functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Retailer Login</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;} .btn-danger{background:#dc3545;}</style>";
echo "</head><body>";

echo "<h1>🧪 Test Retailer Login Functionality</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Check if test retailer exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE username = 'testretailer' AND role = 'retailer'");
    $stmt->execute();
    $testRetailer = $stmt->fetch();
    
    if (!$testRetailer) {
        echo "<div class='warning'>⚠️ Test retailer doesn't exist. Creating one...</div>";
        
        // Create test retailer
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            'Test Retailer',
            'testretailer',
            '<EMAIL>',
            $hashedPassword,
            'retailer',
            'Test Retail Store',
            'Farm Products Retailer'
        ]);
        
        $testRetailerId = $conn->lastInsertId();
        echo "<div class='success'>✅ Created test retailer with ID: $testRetailerId</div>";
        
        // Fetch the newly created retailer
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$testRetailerId]);
        $testRetailer = $stmt->fetch();
    } else {
        echo "<div class='success'>✅ Test retailer exists</div>";
    }
    
    // Display retailer info
    echo "<div class='info'>";
    echo "<strong>Test Retailer Details:</strong><br>";
    echo "• ID: {$testRetailer['id']}<br>";
    echo "• Username: {$testRetailer['username']}<br>";
    echo "• Full Name: {$testRetailer['fullname']}<br>";
    echo "• Email: {$testRetailer['email']}<br>";
    echo "• Role: {$testRetailer['role']}<br>";
    echo "• Company: {$testRetailer['company_name']}<br>";
    echo "</div>";
    
    // Test login simulation
    echo "<h2>🔐 Test Login Simulation</h2>";
    
    session_start();
    
    // Handle login test
    if (isset($_POST['test_login'])) {
        // Simulate successful login
        $_SESSION['logged_in'] = true;
        $_SESSION['user_id'] = $testRetailer['id'];
        $_SESSION['username'] = $testRetailer['username'];
        $_SESSION['fullname'] = $testRetailer['fullname'];
        $_SESSION['email'] = $testRetailer['email'];
        $_SESSION['role'] = $testRetailer['role'];
        $_SESSION['company_name'] = $testRetailer['company_name'];
        $_SESSION['business_type'] = $testRetailer['business_type'];
        $_SESSION['login_time'] = time();
        
        echo "<div class='success'>✅ Login simulation successful!</div>";
        echo "<div class='info'>Session variables set for retailer login</div>";
    }
    
    // Check current session
    if (isset($_SESSION['user_id'])) {
        echo "<div class='info'>";
        echo "<strong>Current Session:</strong><br>";
        echo "• Logged In: " . (isset($_SESSION['logged_in']) ? 'Yes' : 'No') . "<br>";
        echo "• User ID: {$_SESSION['user_id']}<br>";
        echo "• Username: {$_SESSION['username']}<br>";
        echo "• Role: {$_SESSION['role']}<br>";
        echo "• Full Name: {$_SESSION['fullname']}<br>";
        echo "</div>";
        
        if ($_SESSION['role'] === 'retailer') {
            echo "<div class='success'>✅ Session shows retailer role - dashboard should work</div>";
        } else {
            echo "<div class='error'>❌ Session role is '{$_SESSION['role']}', not 'retailer'</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ No active session</div>";
    }
    
    // Test dashboard access
    echo "<h2>🏪 Test Dashboard Access</h2>";
    
    if (file_exists('retailer/dashboard.php')) {
        echo "<div class='success'>✅ Retailer dashboard file exists</div>";
        
        if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'retailer') {
            echo "<div class='success'>✅ Session is valid for retailer dashboard access</div>";
            echo "<div class='info'><a href='retailer/dashboard.php' class='btn btn-primary' target='_blank'>Test Retailer Dashboard</a></div>";
        } else {
            echo "<div class='warning'>⚠️ Session not valid for retailer dashboard - need to login first</div>";
        }
    } else {
        echo "<div class='error'>❌ Retailer dashboard file not found</div>";
    }
    
    // Test actual login process
    echo "<h2>🔑 Test Actual Login Process</h2>";
    
    if (isset($_POST['actual_login'])) {
        $username = 'testretailer';
        $password = 'password123';
        
        // Verify credentials
        $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            echo "<div class='success'>✅ Credentials verified successfully</div>";
            
            // Set session variables (same as login.php)
            $_SESSION['logged_in'] = true;
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['fullname'] = $user['fullname'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['company_name'] = $user['company_name'];
            $_SESSION['business_type'] = $user['business_type'];
            $_SESSION['login_time'] = time();
            
            echo "<div class='success'>✅ Session variables set - login complete</div>";
            
            // Test redirect logic
            $redirectUrl = '';
            switch ($user['role']) {
                case 'retailer':
                    $redirectUrl = 'retailer/dashboard.php';
                    break;
                case 'supplier':
                    $redirectUrl = 'supplier/dashboard.php';
                    break;
                default:
                    $redirectUrl = 'dashboard/dashboard.php';
            }
            
            echo "<div class='info'>🎯 Would redirect to: $redirectUrl</div>";
            
            if (file_exists($redirectUrl)) {
                echo "<div class='success'>✅ Redirect target exists</div>";
                echo "<div class='info'><a href='$redirectUrl' class='btn btn-success' target='_blank'>Go to Dashboard</a></div>";
            } else {
                echo "<div class='error'>❌ Redirect target does not exist</div>";
            }
            
        } else {
            echo "<div class='error'>❌ Credential verification failed</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🛠️ Test Actions</h2>";

echo "<form method='POST' style='margin:20px 0;'>";
echo "<button type='submit' name='test_login' class='btn btn-primary'>Simulate Login</button> ";
echo "<button type='submit' name='actual_login' class='btn btn-success'>Test Actual Login Process</button>";
echo "</form>";

echo "<div style='margin:20px 0;'>";
echo "<a href='login.php' class='btn btn-primary'>Go to Login Page</a> ";
echo "<a href='retailer/dashboard.php' class='btn btn-success'>Test Retailer Dashboard</a> ";
echo "<a href='logout.php' class='btn btn-danger'>Logout</a> ";
echo "<a href='debug_retailer_login.php' class='btn' style='background:#6c757d;'>Debug Tool</a>";
echo "</div>";

echo "<h2>💡 How to Test Retailer Login</h2>";
echo "<div class='info'>";
echo "<strong>Step-by-step test:</strong><br>";
echo "1. Click 'Test Actual Login Process' above to simulate the full login<br>";
echo "2. If successful, click 'Test Retailer Dashboard' to verify dashboard access<br>";
echo "3. Or go to the login page and use these credentials:<br>";
echo "   • Username: <code>testretailer</code><br>";
echo "   • Password: <code>password123</code><br>";
echo "4. After login, you should be redirected to the retailer dashboard";
echo "</div>";

echo "<div class='warning'>";
echo "<strong>⚠️ If login still fails:</strong><br>";
echo "1. Check that the test retailer user exists (should be created above)<br>";
echo "2. Verify the password is correct (password123)<br>";
echo "3. Make sure the retailer dashboard file exists<br>";
echo "4. Check browser console for JavaScript errors<br>";
echo "5. Clear browser cache and cookies";
echo "</div>";

echo "</body></html>";
?>
