<?php
// Debug script for retailer login issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Retailer Login</title>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;} .btn-warning{background:#ffc107;color:#212529;} .btn-danger{background:#dc3545;}</style>";
echo "</head><body>";

echo "<h1>🔍 Debug Retailer Login Issues</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Check current session
    session_start();
    
    echo "<h2>🔐 Current Session Status</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div class='info'>";
        echo "<strong>Active Session:</strong><br>";
        echo "• User ID: " . $_SESSION['user_id'] . "<br>";
        echo "• Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
        echo "• Full Name: " . ($_SESSION['fullname'] ?? 'Not set') . "<br>";
        echo "• Role: " . ($_SESSION['role'] ?? 'Not set') . "<br>";
        echo "• Login Time: " . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'Not set') . "<br>";
        echo "</div>";
        
        if ($_SESSION['role'] === 'retailer') {
            echo "<div class='success'>✅ Currently logged in as retailer</div>";
        } else {
            echo "<div class='warning'>⚠️ Logged in but role is '{$_SESSION['role']}', not 'retailer'</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ No active session found</div>";
    }
    
    // Check all retailer users
    echo "<h2>🏪 All Retailer Users</h2>";
    
    $stmt = $conn->query("SELECT id, username, fullname, email, role, company_name, created_at FROM users WHERE role = 'retailer' ORDER BY id");
    $retailers = $stmt->fetchAll();
    
    if (empty($retailers)) {
        echo "<div class='warning'>⚠️ No retailer users found in database!</div>";
        echo "<div class='info'>This might be why you can't login as a retailer.</div>";
    } else {
        echo "<div class='success'>✅ Found " . count($retailers) . " retailer users</div>";
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Company</th><th>Created</th><th>Actions</th></tr>";
        
        foreach ($retailers as $retailer) {
            echo "<tr>";
            echo "<td>{$retailer['id']}</td>";
            echo "<td><strong>{$retailer['username']}</strong></td>";
            echo "<td>{$retailer['fullname']}</td>";
            echo "<td>{$retailer['email']}</td>";
            echo "<td>{$retailer['company_name']}</td>";
            echo "<td>" . date('M j, Y', strtotime($retailer['created_at'])) . "</td>";
            echo "<td>";
            echo "<a href='#' onclick='testLogin(\"{$retailer['username']}\")' class='btn btn-primary' style='font-size:12px;padding:4px 8px;'>Test Login</a> ";
            echo "<a href='#' onclick='loginAs({$retailer['id']})' class='btn btn-success' style='font-size:12px;padding:4px 8px;'>Login As</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check retailer dashboard file
    echo "<h2>📁 Retailer Dashboard File Check</h2>";
    
    $dashboardPath = 'retailer/dashboard.php';
    if (file_exists($dashboardPath)) {
        echo "<div class='success'>✅ Retailer dashboard file exists: $dashboardPath</div>";
        
        // Check if file is readable
        if (is_readable($dashboardPath)) {
            echo "<div class='success'>✅ Dashboard file is readable</div>";
        } else {
            echo "<div class='error'>❌ Dashboard file exists but is not readable</div>";
        }
        
        // Check file size
        $fileSize = filesize($dashboardPath);
        echo "<div class='info'>📊 File size: " . number_format($fileSize) . " bytes</div>";
        
        if ($fileSize < 100) {
            echo "<div class='warning'>⚠️ File seems very small - might be empty or have errors</div>";
        }
    } else {
        echo "<div class='error'>❌ Retailer dashboard file does not exist: $dashboardPath</div>";
        echo "<div class='warning'>This is likely why retailer login fails!</div>";
    }
    
    // Test login simulation
    echo "<h2>🧪 Login Simulation Test</h2>";
    
    if (!empty($retailers)) {
        $testRetailer = $retailers[0]; // Use first retailer for test
        
        echo "<div class='info'>";
        echo "<strong>Testing login simulation with:</strong><br>";
        echo "• Username: {$testRetailer['username']}<br>";
        echo "• Role: {$testRetailer['role']}<br>";
        echo "</div>";
        
        // Simulate the login redirect logic
        $redirectUrl = '';
        switch ($testRetailer['role']) {
            case 'farmer':
                $redirectUrl = 'dashboard/farmer_dashboard.php';
                break;
            case 'supplier':
                $redirectUrl = 'supplier/dashboard.php';
                break;
            case 'retailer':
                $redirectUrl = 'retailer/dashboard.php';
                break;
            default:
                $redirectUrl = 'dashboard/dashboard.php';
        }
        
        echo "<div class='info'>🎯 Login would redirect to: <strong>$redirectUrl</strong></div>";
        
        if (file_exists($redirectUrl)) {
            echo "<div class='success'>✅ Redirect target file exists</div>";
        } else {
            echo "<div class='error'>❌ Redirect target file does not exist: $redirectUrl</div>";
        }
    }
    
    // Check login.php redirect logic
    echo "<h2>🔍 Login.php Redirect Logic Analysis</h2>";
    
    $loginFile = 'login.php';
    if (file_exists($loginFile)) {
        echo "<div class='success'>✅ Login file exists</div>";
        
        // Read the redirect section
        $loginContent = file_get_contents($loginFile);
        if (strpos($loginContent, "case 'retailer':") !== false) {
            echo "<div class='success'>✅ Retailer case found in login redirect logic</div>";
        } else {
            echo "<div class='error'>❌ Retailer case not found in login redirect logic</div>";
        }
        
        if (strpos($loginContent, 'retailer/dashboard.php') !== false) {
            echo "<div class='success'>✅ Retailer dashboard redirect found in login.php</div>";
        } else {
            echo "<div class='error'>❌ Retailer dashboard redirect not found in login.php</div>";
        }
    } else {
        echo "<div class='error'>❌ Login file does not exist</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

// Handle POST requests for testing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            $userId = intval($_POST['user_id']);
            
            if ($_POST['action'] === 'login_as') {
                $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                
                if ($user) {
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['fullname'] = $user['fullname'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['company_name'] = $user['company_name'];
                    $_SESSION['business_type'] = $user['business_type'];
                    $_SESSION['login_time'] = time();
                    
                    echo "<div class='success'>✅ Successfully logged in as {$user['username']} ({$user['role']})!</div>";
                    
                    // Test redirect
                    if ($user['role'] === 'retailer') {
                        echo "<div class='info'>🎯 Would redirect to: retailer/dashboard.php</div>";
                        echo "<div class='info'><a href='retailer/dashboard.php' class='btn btn-primary'>Test Retailer Dashboard</a></div>";
                    }
                    
                    echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
                } else {
                    echo "<div class='error'>❌ User not found</div>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Action failed: " . $e->getMessage() . "</div>";
    }
}

echo "<h2>🛠️ Quick Actions</h2>";
echo "<div style='margin:20px 0;'>";
echo "<a href='create_test_users.php' class='btn btn-success'>Create Test Users</a> ";
echo "<a href='login.php' class='btn btn-primary'>Go to Login Page</a> ";
echo "<a href='retailer/dashboard.php' class='btn btn-warning'>Test Retailer Dashboard</a> ";
echo "<a href='logout.php' class='btn btn-danger'>Logout</a> ";
echo "<a href='?' class='btn' style='background:#6c757d;'>Refresh</a>";
echo "</div>";

echo "<h2>💡 Common Issues & Solutions</h2>";
echo "<div class='warning'>";
echo "<strong>If you can't login as retailer:</strong><br>";
echo "1. <strong>No retailer users exist</strong> → Use 'Create Test Users' to create a retailer<br>";
echo "2. <strong>Wrong credentials</strong> → Check username/password (default: testretailer/password123)<br>";
echo "3. <strong>Dashboard file missing</strong> → Check if retailer/dashboard.php exists<br>";
echo "4. <strong>Session issues</strong> → Logout and try again<br>";
echo "5. <strong>Role mismatch</strong> → Use debug tools to fix user role";
echo "</div>";

echo "<div class='info'>";
echo "<strong>Test Credentials (if they exist):</strong><br>";
echo "• <strong>Retailer:</strong> Username: <code>testretailer</code>, Password: <code>password123</code><br>";
echo "• <strong>Supplier:</strong> Username: <code>testsupplier</code>, Password: <code>password123</code>";
echo "</div>";

echo "<script>";
echo "function testLogin(username) {";
echo "  alert('To test login with username: ' + username + '\\n\\nGo to login page and use:\\nUsername: ' + username + '\\nPassword: password123');";
echo "  window.open('login.php', '_blank');";
echo "}";

echo "function loginAs(userId) {";
echo "  if (confirm('Login as this retailer user?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"login_as\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";
echo "</script>";

echo "</body></html>";
?>
