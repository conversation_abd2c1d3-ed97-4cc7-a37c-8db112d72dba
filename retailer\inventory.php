<?php
// Start session
session_start();

// Check if user is logged in and has retailer role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'retailer') {
    header("Location: ../login.php");
    exit();
}

// Connect to database
require_once '../config/db_connect.php';

// Initialize variables
$products = [];
$stats = [
    'total_products' => 0,
    'in_stock' => 0,
    'low_stock' => 0,
    'out_of_stock' => 0,
    'total_value' => 0
];

// Fetch inventory for this retailer
try {
    if (!isset($db_connection_error)) {
        $stmt = $conn->prepare("SELECT p.*, s.fullname as supplier_name 
                               FROM products p 
                               JOIN users s ON p.supplier_id = s.id
                               WHERE p.status = 'active'
                               ORDER BY p.stock_quantity ASC");
        $stmt->execute();
        $products = $stmt->fetchAll();

        // Calculate statistics
        $stats['total_products'] = count($products);
        foreach ($products as $product) {
            $stats['total_value'] += $product['price'] * $product['stock_quantity'];

            if ($product['stock_quantity'] <= 0) {
                $stats['out_of_stock']++;
            } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
                $stats['low_stock']++;
            } else {
                $stats['in_stock']++;
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "Error fetching products: " . $e->getMessage();
}

// Helper function to determine stock status
function getStockStatus($quantity, $minLevel) {
    if ($quantity <= 0) {
        return ['status' => 'Out of Stock', 'class' => 'bg-red-100 text-red-800'];
    } elseif ($quantity <= $minLevel) {
        return ['status' => 'Low Stock', 'class' => 'bg-yellow-100 text-yellow-800'];
    } else {
        return ['status' => 'In Stock', 'class' => 'bg-green-100 text-green-800'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiGit - Inventory Management</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header/Navigation -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="/" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                        </div>
                        <nav class="ml-6 flex space-x-8">
                            <a href="dashboard.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Dashboard
                            </a>
                            <a href="inventory.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-primary text-sm font-medium text-gray-900">
                                Inventory
                            </a>
                            <a href="customers.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Customers
                            </a>
                        </nav>
                    </div>
                    <div class="flex items-center">
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="flex items-center max-w-xs rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="user-menu-button">
                                    <span class="sr-only">Open user menu</span>
                                    <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                        <i class="ri-user-line text-gray-400 text-lg flex items-center justify-center h-full"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700"><?php echo htmlspecialchars($_SESSION['fullname']); ?></span>
                                    <i class="ri-arrow-down-s-line ml-1 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Inventory Stats -->
                    <div class="mb-6">
                        <h1 class="text-2xl font-semibold text-gray-900">Inventory Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Monitor your product inventory and stock levels</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-archive-line text-2xl text-primary"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['total_products']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-check-line text-2xl text-green-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">In Stock</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['in_stock']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-alert-line text-2xl text-yellow-500"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['low_stock']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-close-circle-line text-2xl text-red-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['out_of_stock']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Product Inventory</h3>
                            <div class="flex space-x-3">
                                <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-button shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                    <i class="ri-filter-3-line mr-2"></i> Filter
                                </button>
                                <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none">
                                    <i class="ri-shopping-cart-line mr-2"></i> Order Products
                                </button>
                            </div>
                        </div>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($products)): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center justify-center py-8">
                                                <i class="ri-inbox-line text-4xl text-gray-300 mb-2"></i>
                                                <p class="text-lg font-medium">No products found</p>
                                                <p class="text-sm">Your inventory is currently empty.</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($products as $product): ?>
                                        <?php $stockStatus = getStockStatus($product['stock_quantity'], $product['min_stock_level']); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                                                        <?php if (!empty($product['image_url'])): ?>
                                                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="h-10 w-10 rounded-md object-cover">
                                                        <?php else: ?>
                                                            <i class="ri-shopping-bag-line text-gray-400 text-lg"></i>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($product['name']); ?></div>
                                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($product['category']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($product['sku']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($product['supplier_name']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">$<?php echo number_format($product['price'], 2); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo $product['stock_quantity']; ?> units</div>
                                                <div class="text-xs text-gray-500">Min: <?php echo $product['min_stock_level']; ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $stockStatus['class']; ?>">
                                                    <?php echo $stockStatus['status']; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="create_order.php"button class="text-primary hover:text-primary/80 mr-3">Order</button>
                                                <button class="text-gray-600 hover:text-gray-900">Details</button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    &copy; 2025 PiGit. All rights reserved.
                </p>
            </div>
        </footer>
    </div>
</body>
</html>